<template>
	<view class="container">
		<tui-tips backgroundColor="red" color="#fff" :size="30" ref="toast"></tui-tips>
		
		<!-- 主要内容区域 -->
		<view class="content-area">
			<!-- Logo区域 -->
			<view class="logo-area">
				<view class="app-name">砖瓦汇</view>
				<view class="app-desc">专业的砖瓦交易平台</view>
			</view>
			
			<!-- 注册按钮 -->
			<view class="register-area">
				<tui-button type="primary" shape="circle" @click="handleRegister" :loading="loading">
					{{ loading ? '获取协议中...' : '注册' }}
				</tui-button>
			</view>
		</view>
		
		<!-- 协议弹窗 -->
		<tui-modal 
			:show="showAgreement" 
			@cancel="hideAgreement" 
			:custom="true"
			:maskClosable="false"
			width="90%"
		>
			<view class="agreement-modal">
				<view class="agreement-title">用户协议</view>
				<scroll-view class="agreement-content" scroll-y="true">
					<view class="agreement-text">{{ agreementContent }}</view>
				</scroll-view>
				<view class="agreement-buttons">
					<tui-button 
						type="gray" 
						shape="circle" 
						width="45%" 
						height="80rpx"
						@click="hideAgreement"
					>
						取消
					</tui-button>
					<tui-button 
						type="primary" 
						shape="circle" 
						width="45%" 
						height="80rpx"
						@click="agreeAndLogin"
						:loading="loginLoading"
					>
						{{ loginLoading ? '登录中...' : '同意' }}
					</tui-button>
				</view>
			</view>
		</tui-modal>
	</view>
</template>

<script>
	import tui from '../../../common/httpRequest';
	
	export default {
		data() {
			return {
				loading: false,
				loginLoading: false,
				showAgreement: false,
				agreementContent: ''
			}
		},
		onLoad(option) {
			// 页面加载时的初始化逻辑
		},
		methods: {
			// 处理注册按钮点击
			async handleRegister() {
				if (this.loading) return;
				
				this.loading = true;
				
				try {
					// 调用获取协议内容接口
					const res = await tui.request('/api/admin/biz-agreement/getAgreementContent', 'post', {}, false, false, false);
					
					if (res.code === '0000') {
						this.agreementContent = res.data || '暂无协议内容';
						this.showAgreement = true;
					} else {
						this.showToast(res.message || '获取协议失败');
					}
				} catch (error) {
					console.error('获取协议失败:', error);
					this.showToast('网络错误，请稍后重试');
				} finally {
					this.loading = false;
				}
			},
			
			// 隐藏协议弹窗
			hideAgreement() {
				this.showAgreement = false;
				this.agreementContent = '';
			},
			
			// 同意协议并进行微信登录
			async agreeAndLogin() {
				if (this.loginLoading) return;
				
				this.loginLoading = true;
				
				try {
					// 调用微信登录获取code
					const loginRes = await this.getWeixinCode();
					
					if (loginRes.code) {
						// 使用code进行登录
						await this.loginWithCode(loginRes.code);
					} else {
						this.showToast('微信登录失败，请重试');
					}
				} catch (error) {
					console.error('登录失败:', error);
					this.showToast('登录失败，请重试');
				} finally {
					this.loginLoading = false;
				}
			},
			
			// 获取微信登录code
			getWeixinCode() {
				return new Promise((resolve, reject) => {
					uni.login({
						provider: 'weixin',
						success: function(loginRes) {
							resolve(loginRes);
						},
						fail: function(error) {
							reject(error);
						}
					});
				});
			},
			
			// 使用code进行登录
			async loginWithCode(code) {
				const loginData = {
					clientId: "074992be350e4ce98f6a1379dfe84cba",
					grantType: "applet",
					code: code
				};
				
				try {
					const res = await tui.request('/api/admin/auth/login', 'post', loginData, false, false, false);
					
					if (res.code === '0000') {
						// 存储token和用户信息
						uni.setStorageSync('token', res.data.accessToken);
						uni.setStorageSync('userInfo', res.data.userInfo);
						
						// 为了兼容可能的其他代码，也存储一些常用字段
						uni.setStorageSync('user', res.data.userInfo.nickname);
						uni.setStorageSync('userId', res.data.userInfo.userId);
						
						this.showToast('注册成功！', true);
						
						// 延迟跳转到主页
						setTimeout(() => {
							uni.switchTab({
								url: '/pages/tabbar/main/index'
							});
						}, 1500);
					} else {
						this.showToast(res.message || '登录失败');
					}
				} catch (error) {
					console.error('登录请求失败:', error);
					this.showToast('网络错误，请稍后重试');
				}
			},
			
			// 显示提示信息
			showToast(message, success = false) {
				const options = {
					msg: message,
					duration: 2000
				};
				this.$refs.toast.showTips(options);
			}
		}
	};
</script>

<style scoped>
	.container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		height: 100vh;
		background-image: url('https://scwl.kingvisus.com/h5/static/images/login.png');
		background-size: cover;
		background-position: center;
	}
	
	.content-area {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		width: 80%;
		padding: 40rpx;
		background-color: rgba(255, 255, 255, 0.95);
		border-radius: 20rpx;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
	}
	
	.logo-area {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 80rpx;
	}
	
	.app-name {
		font-size: 48rpx;
		color: #333;
		font-weight: bold;
		margin-bottom: 20rpx;
	}
	
	.app-desc {
		font-size: 28rpx;
		color: #666;
		text-align: center;
	}
	
	.register-area {
		width: 100%;
	}
	
	/* 协议弹窗样式 */
	.agreement-modal {
		padding: 40rpx;
		display: flex;
		flex-direction: column;
		height: 70vh;
	}
	
	.agreement-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		text-align: center;
		margin-bottom: 30rpx;
		padding-bottom: 20rpx;
		border-bottom: 1rpx solid #eee;
	}
	
	.agreement-content {
		flex: 1;
		margin-bottom: 30rpx;
		max-height: 50vh;
	}
	
	.agreement-text {
		font-size: 28rpx;
		line-height: 1.6;
		color: #666;
		padding: 20rpx;
		background-color: #f8f8f8;
		border-radius: 10rpx;
	}
	
	.agreement-buttons {
		display: flex;
		justify-content: space-between;
		align-items: center;
		gap: 20rpx;
	}
</style>
