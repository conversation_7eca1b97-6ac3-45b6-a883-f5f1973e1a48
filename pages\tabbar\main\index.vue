<template>
	<view class="content" :style="{ paddingTop: contentTop }">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-title">砖瓦汇</view>
			<view class="header-login" @click="goLogin">登录/注册</view>
		</view>

		<!-- 首页广告轮播图 -->
		<view class="swiper_box">
			<swiper class="swiper" circular :autoplay="true" :interval="3000" :duration="500" v-if="bannerList.length > 0">
				<swiper-item v-for="(item, index) in bannerList" :key="index">
					<view class="swiper-item">
						<image :src="item" mode="aspectFill"></image>
					</view>
				</swiper-item>
			</swiper>
			<view v-else class="swiper-placeholder">
				<text>首页广告(图)</text>
			</view>
		</view>

		<!-- 功能按钮区域 -->
		<view class="function-buttons">
			<tui-grid :unlined="true">
				<tui-grid-item :cell="2" :border="false" @click="goBrickPurchase">
					<view class="function-item">
						<text class="function-text">砖材购买</text>
					</view>
				</tui-grid-item>
				<tui-grid-item :cell="2" :border="false" @click="goTilePurchase">
					<view class="function-item">
						<text class="function-text">瓦片购买</text>
					</view>
				</tui-grid-item>
			</tui-grid>
		</view>

		<!-- 公示区域 -->
		<view class="notice-section">
			<tui-section title="公示" :showLine="true"></tui-section>
			<scroll-view class="notice-list" scroll-y="true" :show-scrollbar="true">
				<tui-list-cell
					v-for="(item, index) in noticeList"
					:key="index"
					:arrow="true"
					:index="index"
					@click="goDetail(item.agreementId)"
				>
					<view class="notice-item">
						<text class="notice-title">{{ item.title }}</text>
					</view>
				</tui-list-cell>
				<view v-if="noticeList.length === 0" class="no-data">
					<text>暂无公示信息</text>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import tui from '../../../common/httpRequest';

	export default {
		data() {
			return {
				statusBarHeight: uni.getStorageSync('menuInfo').statusBarHeight,
				contentTop: uni.getStorageSync('menuInfo').contentTop,
				bannerList: [], // 广告轮播图列表
				noticeList: [] // 公示列表
			};
		},
		onLoad() {
			this.getMiniAppHome();
		},
		methods: {
			// 获取首页数据
			getMiniAppHome() {
				tui.request('/api/admin/biz-agreement/getMiniAppHome', 'post', {}, false, false, false)
					.then(res => {
						if (res.code === '0000') {
							this.bannerList = res.data.urls || [];
							this.noticeList = res.data.list || [];
						} else {
							tui.toast(res.message || '获取数据失败');
						}
					})
					.catch(err => {
						console.error('获取首页数据失败:', err);
						tui.toast('网络错误，请稍后重试');
					});
			},
			// 跳转登录页面
			goLogin() {
				// 显示选择登录或注册的弹窗
				uni.showActionSheet({
					itemList: ['登录', '注册'],
					success: (res) => {
						if (res.tapIndex === 0) {
							// 跳转到登录页面
							uni.navigateTo({
								url: '/pages/common/login/index'
							});
						} else if (res.tapIndex === 1) {
							// 跳转到注册页面
							uni.navigateTo({
								url: '/pages/common/register/index'
							});
						}
					}
				});
			},
			// 砖材购买
			goBrickPurchase() {
				tui.toast('砖材购买功能开发中');
			},
			// 瓦片购买
			goTilePurchase() {
				tui.toast('瓦片购买功能开发中');
			},
			// 跳转详情页
			goDetail(agreementId) {
				if (!agreementId) {
					tui.toast('参数错误');
					return;
				}
				uni.navigateTo({
					url: `/pages/tabbar/main/detail?agreementId=${agreementId}`
				});
			}
		}
	};
</script>

<style lang="scss">
	.content {
		min-height: 100vh;
		box-sizing: border-box;
		background-color: #f5f5f5;

		// 顶部导航栏
		.header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			padding: 20rpx 30rpx;
			background-color: #fff;
			border-bottom: 1px solid #eee;

			.header-title {
				font-size: 36rpx;
				font-weight: bold;
				color: #333;
			}

			.header-login {
				font-size: 28rpx;
				color: #1469f8;
				padding: 10rpx 20rpx;
				border: 1px solid #1469f8;
				border-radius: 20rpx;
			}
		}

		// 轮播图区域
		.swiper_box {
			width: 100%;
			height: 420rpx;
			margin: 20rpx 0;

			.swiper {
				width: 100%;
				height: 100%;
				border-radius: 20rpx;
				overflow: hidden;
				margin: 0 20rpx;
				width: calc(100% - 40rpx);

				.swiper-item {
					display: flex;
					flex-direction: row;
					overflow: hidden;
					width: 100%;
					height: 100%;

					image {
						display: block;
						flex: 1;
						height: 100%;
					}
				}
			}

			.swiper-placeholder {
				display: flex;
				align-items: center;
				justify-content: center;
				width: calc(100% - 40rpx);
				height: 100%;
				margin: 0 20rpx;
				background-color: #f0f0f0;
				border: 2px dashed #ffa500;
				border-radius: 20rpx;

				text {
					font-size: 32rpx;
					color: #999;
				}
			}
		}

		// 功能按钮区域
		.function-buttons {
			margin: 20rpx;

			.function-item {
				display: flex;
				align-items: center;
				justify-content: center;
				height: 200rpx;
				background-color: #fff;
				border: 2px solid #ffa500;
				border-radius: 20rpx;
				margin: 10rpx;

				.function-text {
					font-size: 32rpx;
					color: #333;
					font-weight: bold;
				}
			}
		}

		// 公示区域
		.notice-section {
			margin: 20rpx;
			background-color: #fff;
			border-radius: 20rpx;
			overflow: hidden;

			.notice-list {
				// 设置最小高度显示2条，最大高度显示5条
				min-height: 200rpx;  // 约2条的高度
				max-height: 500rpx;  // 约5条的高度

				.notice-item {
					display: flex;
					align-items: center;
					width: 100%;

					.notice-title {
						font-size: 28rpx;
						color: #333;
						line-height: 1.4;
						// 限制显示2行，超出显示省略号
						display: -webkit-box;
						-webkit-box-orient: vertical;
						-webkit-line-clamp: 2;
						line-clamp: 2;
						overflow: hidden;
						text-overflow: ellipsis;
						word-break: break-all;
					}
				}

				.no-data {
					display: flex;
					align-items: center;
					justify-content: center;
					height: 200rpx;

					text {
						font-size: 28rpx;
						color: #999;
					}
				}
			}
		}
	}
</style>